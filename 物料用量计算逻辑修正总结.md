# 物料用量计算逻辑修正总结

## 修改概述

本次修改解决了项目计划中物料用量计算逻辑的错误问题。原代码使用了底层物料的**装配数量**(`assembleQuantity`)进行计算，现已修正为使用**计划用量**(`planNum`)。

## 问题描述

在 `CostProjectPlanApplicationService.java` 文件中，存在两处物料用量计算逻辑错误：

1. **第631-635行**：`calculateProductDirectCost` 方法中的物料用量计算
2. **第753-757行**：`generateMaterialSummaryFromBottomMaterials` 方法中的物料用量计算

这两处都错误地使用了 `material.getAssembleQuantity()` 来计算物料实际用量。

## 修改详情

### 修改位置1：calculateProductDirectCost 方法

**修改前：**
```java
// 计算该物料的实际用量 = 底层物料装配数量 * 产品总用量
BigDecimal materialUsage = BigDecimal.ZERO;
if (material.getAssembleQuantity() != null && totalUsage != null) {
    materialUsage = material.getAssembleQuantity().multiply(totalUsage);
}
```

**修改后：**
```java
// 计算该物料的实际用量 = 底层物料计划用量 * 产品总用量
BigDecimal materialUsage = BigDecimal.ZERO;
if (material.getPlanNum() != null && totalUsage != null) {
    materialUsage = material.getPlanNum().multiply(totalUsage);
}
```

### 修改位置2：generateMaterialSummaryFromBottomMaterials 方法

**修改前：**
```java
// 计算该物料的实际用量 = 底层物料装配数量 * 产品总用量
BigDecimal materialUsage = BigDecimal.ZERO;
if (material.getAssembleQuantity() != null && totalUsage != null) {
    materialUsage = material.getAssembleQuantity().multiply(totalUsage);
}

// 添加调试日志
log.debug("处理物料: {}, 装配数量: {}, 产品总用量: {}, 计算用量: {}",
    material.getMaterialCode(),
    material.getAssembleQuantity(),
    totalUsage,
    materialUsage);
```

**修改后：**
```java
// 计算该物料的实际用量 = 底层物料计划用量 * 产品总用量
BigDecimal materialUsage = BigDecimal.ZERO;
if (material.getPlanNum() != null && totalUsage != null) {
    materialUsage = material.getPlanNum().multiply(totalUsage);
}

// 添加调试日志
log.debug("处理物料: {}, 计划用量: {}, 产品总用量: {}, 计算用量: {}",
    material.getMaterialCode(),
    material.getPlanNum(),
    totalUsage,
    materialUsage);
```

## 字段说明

根据 `ProductFileTreeEntity` 实体类的定义：

- **assembleQuantity**：装配数量 - 表示在装配过程中的数量
- **planNum**：计划用量 - 表示计划中实际需要的用量

在成本计算中，应该使用**计划用量**而非装配数量，因为计划用量更准确地反映了实际的物料需求。

## 计算公式

修正后的物料用量计算公式为：

```
物料实际用量 = 底层物料计划用量 × 产品总用量
```

其中：
- **底层物料计划用量**：来自产品配方中的 `material.getPlanNum()`
- **产品总用量**：项目计划中相同产品型号的用量总和

## 影响范围

此修改影响以下功能：

1. **直接成本计算**：`calculateProductDirectCost` 方法
2. **原料明细汇总**：`generateMaterialSummaryFromBottomMaterials` 方法
3. **项目计划预算计算**：依赖上述两个方法的所有计算逻辑

## 编译结果

✅ **编译成功** - 所有模块编译通过，无语法错误

```
[INFO] BUILD SUCCESS
[INFO] Total time: 15.785 s
```

## 测试建议

建议在以下场景进行测试验证：

1. **单产品计算**：验证单个产品的物料用量计算是否正确
2. **多产品汇总**：验证多个相同产品型号的用量汇总是否正确
3. **原料明细**：验证原料明细汇总中的物料用量是否准确
4. **成本计算**：验证整体的成本计算结果是否符合预期

## 记忆更新

已将此次修改的规则添加到项目记忆中：

> 物料用量计算逻辑修正：在CostProjectPlanApplicationService中，物料实际用量计算应使用底层物料的计划用量(planNum)而非装配数量(assembleQuantity)，计算公式为：物料实际用量 = 底层物料计划用量 * 产品总用量

---

**修改完成时间**：2025-07-28 14:52:26  
**修改状态**：✅ 已完成并编译通过
