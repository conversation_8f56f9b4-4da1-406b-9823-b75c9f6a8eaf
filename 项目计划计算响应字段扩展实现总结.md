# 项目计划计算响应字段扩展实现总结

## 任务概述

在 `CostProjectPlanController.java` 文件中的 `calculateBudgetBasisByData` 接口，需要在响应对象 `CostProjectPlanCalculateResponse` 中添加一个缺失的字段：**合同/预估收入（税后万元）**。

## 需求分析

### 业务需求
- **字段名称**：`contractEstimatedIncomeAfterTax`（合同/预估收入（税后万元））
- **计算公式**：合同/预估收入（税后万元） = 年度应收预算（油，单位万元）合计 + 年度应收预算（水，单位万元）合计
- **数据来源**：项目计划明细中的 `revenueOil` 和 `revenueWater` 字段汇总

### 技术分析
通过代码分析发现，系统中已经存在 `totalRevenue`（年度预算应收总计）字段，其计算逻辑正好符合需求：
```java
// 计算年度预算应收总计
BigDecimal totalRevenue = projectPlan.getCostProjectPlanDetailList().stream()
    .map(detail -> {
        BigDecimal total = BigDecimal.ZERO;
        if (detail.getRevenueOil() != null) {
            total = total.add(detail.getRevenueOil());
        }
        if (detail.getRevenueWater() != null) {
            total = total.add(detail.getRevenueWater());
        }
        return total;
    })
    .reduce(BigDecimal.ZERO, BigDecimal::add);
```

## 实现方案

### 修改文件清单
1. **sftd-module-cost-api/src/main/java/com/cdkit/modules/cm/api/projectplan/dto/CostProjectPlanCalculateResponse.java**
   - 添加 `contractEstimatedIncomeAfterTax` 字段

2. **sftd-module-cost-application/src/main/java/com/cdkit/modules/cm/application/projectplan/CostProjectPlanApplicationService.java**
   - 在三个计算方法中设置新字段值

### 具体实现

#### 1. 响应DTO字段扩展

在 `CostProjectPlanCalculateResponse` 类中添加新字段：

```java
/**合同/预估收入（税后万元）*/
@Schema(description = "合同/预估收入（税后万元）")
private BigDecimal contractEstimatedIncomeAfterTax;
```

#### 2. 应用服务层逻辑修改

在 `CostProjectPlanApplicationService` 的三个计算方法中添加字段设置：

**方法1：`calculateBudgetBasisByData(CostProjectPlanCalculateRequest request)`**
```java
// 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
response.setContractEstimatedIncomeAfterTax(totalRevenue);
```

**方法2：`calculateBudgetBasis(String planId)`**
```java
// 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
response.setContractEstimatedIncomeAfterTax(totalRevenue);
```

**方法3：`calculateAndSaveBudgetBasis(String planId)`**
```java
// 设置合同/预估收入（税后万元） = 年度应收预算（油）合计 + 年度应收预算（水）合计
response.setContractEstimatedIncomeAfterTax(totalRevenue);
```

## 计算逻辑说明

### 数据流程
1. **数据来源**：项目计划明细表（`CostProjectPlanDetailEntity`）
2. **基础计算**：
   - `revenueOil` = 预计年处理量（油，方） × 费率 ÷ 10000
   - `revenueWater` = 预计年处理量（水，方） × 费率 ÷ 10000
3. **汇总计算**：
   - `totalRevenue` = Σ(revenueOil + revenueWater)
   - `contractEstimatedIncomeAfterTax` = totalRevenue

### 业务含义
- **年度应收预算（油）**：基于油类处理量和费率计算的预期收入
- **年度应收预算（水）**：基于水类处理量和费率计算的预期收入
- **合同/预估收入（税后万元）**：项目总预期收入，用于成本预算和利润分析

## 测试验证

### 编译测试
执行 `mvn clean compile -DskipTests` 命令，编译成功：
```
[INFO] BUILD SUCCESS
[INFO] Total time: 21.293 s
```

### 功能验证点
1. **字段存在性**：响应对象包含 `contractEstimatedIncomeAfterTax` 字段
2. **数值正确性**：字段值等于 `totalRevenue`
3. **计算一致性**：三个计算方法都正确设置了该字段

## 影响范围分析

### 正面影响
- ✅ 满足业务需求，提供合同收入预估数据
- ✅ 保持与现有计算逻辑的一致性
- ✅ 不影响现有功能和数据结构

### 风险评估
- 🟢 **低风险**：仅添加字段，不修改现有逻辑
- 🟢 **向后兼容**：不影响现有API调用
- 🟢 **数据安全**：复用现有计算结果，无额外查询

## 部署说明

### 部署步骤
1. 部署更新后的jar包
2. 重启相关服务
3. 验证API响应包含新字段

### 回滚方案
如需回滚，可以：
1. 恢复原始代码版本
2. 重新编译部署
3. 前端忽略新字段（向后兼容）

## 总结

本次实现成功在项目计划计算响应中添加了"合同/预估收入（税后万元）"字段，通过复用现有的 `totalRevenue` 计算逻辑，确保了数据的准确性和一致性。实现方案简洁高效，风险可控，满足业务需求。

### 关键成果
- ✅ 新增字段：`contractEstimatedIncomeAfterTax`
- ✅ 计算逻辑：油收入 + 水收入
- ✅ 覆盖范围：三个计算方法全部支持
- ✅ 编译通过：无语法错误
- ✅ 向后兼容：不影响现有功能

### 技术特点
- **代码复用**：充分利用现有 `totalRevenue` 计算逻辑
- **一致性保证**：三个方法统一实现
- **注释清晰**：中文注释说明字段含义和计算公式
- **类型安全**：使用 `BigDecimal` 确保精度
